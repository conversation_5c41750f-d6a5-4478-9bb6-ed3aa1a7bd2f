"use client"

import { useState } from "react"
import Link from "next/link"
import { ArrowRight, Search, Calendar, FileText, BookOpen, Users, Clock, Eye, Filter, Grid3X3, List, Sparkles, TrendingUp } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Post {
  id: string
  title: string
  slug: string
  excerpt?: string | null
  featured: boolean
  imageUrl?: string | null
  imageAlt?: string | null
  viewCount: number
  readingTime?: string | null
  publishedAt?: Date | null
  author: {
    name: string | null
    email: string
  }
  category: {
    name: string
    slug: string
    color?: string | null
  }
  tags: Array<{
    tag: {
      name: string
      slug: string
    }
  }>
}

interface Category {
  id: string
  name: string
  slug: string
  color?: string | null
  _count: {
    posts: number
  }
}

interface PostsPageClientProps {
  posts: Post[]
  categories: Category[]
}

function formatDate(date: Date | null) {
  if (!date) return 'Not published'
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(new Date(date))
}

export function PostsPageClient({ posts, categories }: PostsPageClientProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeCategory, setActiveCategory] = useState("all")
  const [sortBy, setSortBy] = useState("newest")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")

  // Filter posts based on search term and active category
  let filteredPosts = posts.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (post.excerpt && post.excerpt.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         post.tags.some(tag => tag.tag.name.toLowerCase().includes(searchTerm.toLowerCase()))

    const matchesCategory = activeCategory === "all" || post.category.slug === activeCategory

    return matchesSearch && matchesCategory
  })

  // Sort posts
  filteredPosts = filteredPosts.sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return new Date(b.publishedAt || 0).getTime() - new Date(a.publishedAt || 0).getTime()
      case "oldest":
        return new Date(a.publishedAt || 0).getTime() - new Date(b.publishedAt || 0).getTime()
      case "popular":
        return b.viewCount - a.viewCount
      case "title":
        return a.title.localeCompare(b.title)
      default:
        return 0
    }
  })

  // Get featured post if any
  const featuredPost = posts.find(post => post.featured)

  // Get stats
  const totalPosts = posts.length
  const totalViews = posts.reduce((sum, post) => sum + post.viewCount, 0)
  const categoriesCount = categories.length

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-muted/3 to-background">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 right-1/6 w-96 h-96 bg-gradient-to-br from-crimson/4 via-crimson/2 to-transparent rounded-full blur-3xl animate-gentle-float"></div>
        <div className="absolute bottom-1/4 left-1/6 w-80 h-80 bg-gradient-to-br from-gold/6 via-gold/3 to-transparent rounded-full blur-3xl animate-gentle-float" style={{ animationDelay: '3s' }}></div>
        <div className="absolute inset-0 opacity-[0.01]" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px)`,
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Academic Header Section */}
        <div className="py-16 md:py-20 text-center">
          <div className="inline-flex items-center gap-4 mb-8">
            <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-crimson/40 to-crimson/20"></div>
            <Badge className="px-4 py-2 bg-crimson/5 border-crimson/30 text-crimson">
              <BookOpen className="h-4 w-4 mr-2" />
              Academic Publications & News
            </Badge>
            <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-crimson/40 to-crimson/20"></div>
          </div>

          <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-8">
            <span className="block text-foreground">Knowledge</span>
            <span className="block bg-gradient-to-r from-crimson via-crimson to-gold bg-clip-text text-transparent">
              Repository
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed mb-8">
            Discover cutting-edge research, academic insights, and institutional updates from our distinguished faculty and vibrant college community.
          </p>

          {/* Academic Stats */}
          <div className="grid grid-cols-3 gap-8 max-w-2xl mx-auto mt-12">
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-crimson mb-2">{totalPosts}</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wide">Publications</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-gold mb-2">{totalViews.toLocaleString()}</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wide">Total Reads</div>
            </div>
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-foreground mb-2">{categoriesCount}</div>
              <div className="text-sm text-muted-foreground uppercase tracking-wide">Categories</div>
            </div>
          </div>
        </div>

        {/* Enhanced Search and Filter Section */}
        <div className="bg-background/50 backdrop-blur-sm rounded-2xl border border-border/30 p-8 mb-12 shadow-lg">
          <div className="space-y-6">
            {/* Search Bar */}
            <div className="relative max-w-2xl mx-auto">
              <Search className="absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search publications, research, and insights..."
                className="pl-12 h-12 text-lg border-border/50 focus:border-crimson/50 bg-background/80"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            {/* Filter Controls */}
            <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
              {/* Category Tabs */}
              <Tabs defaultValue="all" className="flex-1" onValueChange={setActiveCategory}>
                <TabsList className="w-full max-w-4xl mx-auto justify-start overflow-x-auto py-3 bg-muted/50">
                  <TabsTrigger value="all" className="rounded-lg px-4 py-2">
                    <Sparkles className="h-4 w-4 mr-2" />
                    All ({totalPosts})
                  </TabsTrigger>
                  {categories.map(category => (
                    <TabsTrigger
                      key={category.slug}
                      value={category.slug}
                      className="rounded-lg px-4 py-2"
                    >
                      {category.name} ({category._count.posts})
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>

              {/* Sort and View Controls */}
              <div className="flex items-center gap-3">
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="newest">
                      <div className="flex items-center">
                        <TrendingUp className="h-4 w-4 mr-2" />
                        Newest
                      </div>
                    </SelectItem>
                    <SelectItem value="oldest">Oldest</SelectItem>
                    <SelectItem value="popular">
                      <div className="flex items-center">
                        <Eye className="h-4 w-4 mr-2" />
                        Most Read
                      </div>
                    </SelectItem>
                    <SelectItem value="title">Title A-Z</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex border border-border/50 rounded-lg p-1">
                  <Button
                    variant={viewMode === "grid" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("grid")}
                    className="px-3"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setViewMode("list")}
                    className="px-3"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Featured Publication */}
        {featuredPost && activeCategory === "all" && searchTerm === "" && (
          <div className="mb-16">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-[1px] bg-gradient-to-r from-transparent to-gold/40"></div>
              <Badge className="px-4 py-2 bg-gold/10 border-gold/30 text-gold">
                <Sparkles className="h-4 w-4 mr-2" />
                Featured Publication
              </Badge>
              <div className="w-12 h-[1px] bg-gradient-to-l from-transparent to-gold/40"></div>
            </div>

            <div className="relative bg-gradient-to-br from-background/80 via-muted/10 to-background/80 rounded-3xl overflow-hidden shadow-2xl border border-border/20 backdrop-blur-sm">
              {/* Background glow */}
              <div className="absolute inset-0 bg-gradient-to-br from-gold/5 to-crimson/5 opacity-50"></div>

              <div className="relative md:flex">
                <div className="md:w-1/2 relative overflow-hidden">
                  <img
                    src={featuredPost.imageUrl || "/images/campus/main-building.jpg"}
                    alt={featuredPost.imageAlt || featuredPost.title}
                    className="h-80 md:h-full w-full object-cover hover:scale-105 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                </div>

                <div className="p-8 md:p-12 md:w-1/2 flex flex-col justify-between">
                  <div>
                    <Badge
                      className="mb-4 px-3 py-1"
                      style={{
                        backgroundColor: featuredPost.category.color || '#e5e7eb',
                        color: '#374151'
                      }}
                    >
                      {featuredPost.category.name}
                    </Badge>

                    <h3 className="text-3xl md:text-4xl font-bold mb-6 leading-tight">
                      <Link href={`/posts/${featuredPost.slug}`} className="hover:text-crimson transition-colors">
                        {featuredPost.title}
                      </Link>
                    </h3>

                    <p className="text-lg text-muted-foreground mb-6 leading-relaxed">{featuredPost.excerpt}</p>
                  </div>

                  <div className="space-y-6">
                    <div className="flex items-center gap-6 text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4" />
                        <span>{formatDate(featuredPost.publishedAt)}</span>
                      </div>
                      {featuredPost.readingTime && (
                        <div className="flex items-center">
                          <Clock className="mr-2 h-4 w-4" />
                          <span>{featuredPost.readingTime}</span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <Eye className="mr-2 h-4 w-4" />
                        <span>{featuredPost.viewCount} reads</span>
                      </div>
                    </div>

                    <Button asChild size="lg" className="bg-gradient-to-r from-crimson to-gold hover:from-crimson/90 hover:to-gold/90">
                      <Link href={`/posts/${featuredPost.slug}`}>
                        Read Full Publication <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

      {/* All posts */}
      <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
        {filteredPosts.map((post) => (
          <Card key={post.id} className="flex flex-col overflow-hidden rounded-lg shadow-lg border-0 transition-all duration-300 hover:shadow-xl">
            <div className="h-48 overflow-hidden">
              <img
                src={post.imageUrl || "/placeholder.svg?height=400&width=600"}
                alt={post.imageAlt || post.title}
                className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
              />
            </div>
            <CardHeader>
              <div className="flex items-center justify-between mb-2">
                <Badge 
                  variant="outline" 
                  className="text-xs font-medium"
                  style={{ 
                    backgroundColor: post.category.color || '#e5e7eb',
                    color: '#374151'
                  }}
                >
                  {post.category.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {formatDate(post.publishedAt)}
                </span>
              </div>
              <CardTitle className="line-clamp-2">
                <Link href={`/posts/${post.slug}`} className="hover:text-primary transition-colors">
                  {post.title}
                </Link>
              </CardTitle>
            </CardHeader>
            <CardContent className="flex-grow">
              <p className="text-sm text-muted-foreground line-clamp-3">{post.excerpt}</p>
              {post.readingTime && (
                <p className="text-xs text-muted-foreground mt-2">{post.readingTime}</p>
              )}
            </CardContent>
            <CardFooter className="flex flex-col items-start gap-4 pt-4 border-t">
              <div className="flex flex-wrap gap-2">
                {post.tags.slice(0, 3).map(({ tag }) => (
                  <Badge key={tag.slug} variant="secondary" className="text-xs">
                    {tag.name}
                  </Badge>
                ))}
                {post.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">+{post.tags.length - 3}</Badge>
                )}
              </div>
              <div className="flex items-center justify-between w-full">
                <Link
                  href={`/posts/${post.slug}`}
                  className="text-sm font-medium text-primary hover:underline flex items-center"
                >
                  Read More <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
                <span className="text-xs text-muted-foreground">
                  {post.viewCount} views
                </span>
              </div>
            </CardFooter>
          </Card>
        ))}
      </div>

      {/* Empty state */}
      {filteredPosts.length === 0 && (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium mb-2">No posts found</h3>
          <p className="text-muted-foreground mb-6">Try adjusting your search or filter criteria</p>
          <Button onClick={() => {setSearchTerm(""); setActiveCategory("all");}}>
            Reset Filters
          </Button>
        </div>
      )}

      {/* Newsletter subscription */}
      <div className="mt-16 bg-muted rounded-xl p-8 text-center">
        <h2 className="text-2xl font-bold mb-2">Stay Updated</h2>
        <p className="text-muted-foreground mb-6 max-w-md mx-auto">
          Subscribe to our newsletter to receive the latest posts and updates directly in your inbox.
        </p>
        <form className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
          <Input
            type="email"
            placeholder="Enter your email"
            className="flex-grow"
            required
          />
          <Button type="submit">Subscribe</Button>
        </form>
      </div>
    </div>
  )
}
