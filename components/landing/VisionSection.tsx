'use client'

import { Badge } from "@/components/ui/badge"
import { Globe, Lightbulb, Users, Cpu, Heart, BookOpen, Brain, Palette } from "lucide-react"

const visionElements = [
  {
    icon: <Cpu className="h-12 w-12" />,
    title: "Technologically Advanced",
    subtitle: "Innovation at Core",
    description: "Embracing cutting-edge technology to shape tomorrow's solutions",
    color: "text-blue-600",
    accentColor: "border-l-blue-400",
    bgPattern: "bg-gradient-to-br from-blue-50/60 to-cyan-50/40",
    hoverBg: "hover:from-blue-50/80 hover:to-cyan-50/60",
  },
  {
    icon: <Heart className="h-12 w-12" />,
    title: "Ethically Grounded",
    subtitle: "Moral Foundation",
    description: "Building character alongside competence in every endeavor",
    color: "text-crimson",
    accentColor: "border-l-rose-400",
    bgPattern: "bg-gradient-to-br from-red-50/60 to-pink-50/40",
    hoverBg: "hover:from-red-50/80 hover:to-pink-50/60",
  },
  {
    icon: <BookOpen className="h-12 w-12" />,
    title: "Interdisciplinary Learning",
    subtitle: "Beyond Boundaries",
    description: "Weaving knowledge across fields to create holistic understanding",
    color: "text-purple-600",
    accentColor: "border-l-purple-400",
    bgPattern: "bg-gradient-to-br from-purple-50/60 to-indigo-50/40",
    hoverBg: "hover:from-purple-50/80 hover:to-indigo-50/60",
  },
  {
    icon: <Lightbulb className="h-12 w-12" />,
    title: "Creative Problem-Solving",
    subtitle: "Innovative Thinking",
    description: "Transforming challenges into opportunities through creative insight",
    color: "text-amber-600",
    accentColor: "border-l-amber-400",
    bgPattern: "bg-gradient-to-br from-yellow-50/60 to-orange-50/40",
    hoverBg: "hover:from-yellow-50/80 hover:to-orange-50/60",
  },
  {
    icon: <Globe className="h-12 w-12" />,
    title: "Global Mindset",
    subtitle: "World Perspective",
    description: "Cultivating citizens who think locally and act globally",
    color: "text-green-600",
    accentColor: "border-l-emerald-400",
    bgPattern: "bg-gradient-to-br from-green-50/60 to-emerald-50/40",
    hoverBg: "hover:from-green-50/80 hover:to-emerald-50/60",
  },
  {
    icon: <Brain className="h-12 w-12" />,
    title: "Deep Thinking",
    subtitle: "Intellectual Depth",
    description: "Nurturing contemplative minds that question and understand",
    color: "text-indigo-600",
    accentColor: "border-l-indigo-400",
    bgPattern: "bg-gradient-to-br from-indigo-50/60 to-blue-50/40",
    hoverBg: "hover:from-indigo-50/80 hover:to-blue-50/60",
  },
]

export default function VisionSection() {
  return (
    <section id="vision" className="py-20 md:py-28 lg:py-32 bg-gradient-to-b from-background to-muted/8 relative overflow-hidden scroll-mt-20">
      {/* Minimal Background Pattern */}
      <div className="absolute inset-0 opacity-[0.006]">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25px 25px, #c82f48 1px, transparent 0)`,
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      {/* Subtle ambient elements */}
      <div className="absolute top-1/4 right-1/4 w-80 h-80 bg-crimson/4 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-gold/5 rounded-full blur-3xl"></div>

      <div className="px-6 md:px-8 lg:px-12 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Clean Header */}
          <div className="text-center mb-16 md:mb-20">
            <div className="inline-flex items-center gap-4 mb-8">
              <div className="w-12 h-[1px] bg-crimson/30"></div>
              <Badge variant="outline" className="px-4 py-2 font-medium border-crimson/30 text-crimson bg-crimson/5">
                <Palette className="h-3 w-3 mr-2" />
                Our Vision
              </Badge>
              <div className="w-12 h-[1px] bg-crimson/30"></div>
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-8 tracking-tight">
              Setting Standards of{" "}
              <span className="text-crimson">
                Excellence
              </span>
            </h2>

            <div className="max-w-4xl mx-auto space-y-4">
              <p className="text-lg md:text-xl text-muted-foreground leading-relaxed">
                Ullens College aims to set standards of excellence by nurturing{" "}
                <span className="text-foreground font-medium">technologically advanced</span>{" "}
                and{" "}
                <span className="text-crimson font-medium">ethically grounded</span>{" "}
                graduates
              </p>
              <p className="text-base md:text-lg text-muted-foreground/85 leading-relaxed">
                who embrace{" "}
                <span className="text-foreground font-medium">interdisciplinary learning</span>
                , think deeply, solve problems creatively, and act with a{" "}
                <span className="text-gold font-medium">global mindset</span>{" "}
                in a rapidly changing world.
              </p>
            </div>
          </div>

          {/* Clean Grid Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8 mb-16">
            {visionElements.map((element, index) => (
              <div
                key={index}
                className="group bg-background/50 rounded-xl p-6 border border-border/30 hover:border-crimson/30 hover:shadow-lg transition-all duration-300 backdrop-blur-sm"
              >
                <div className="flex items-start gap-4">
                  <div className={`${element.color} flex-shrink-0 group-hover:scale-105 transition-transform duration-300`}>
                    {element.icon}
                  </div>
                  <div className="flex-1">
                    <div className="mb-3">
                      <h3 className="text-lg font-semibold text-foreground leading-tight">
                        {element.title}
                      </h3>
                      <p className="text-sm text-muted-foreground font-medium mt-1">
                        {element.subtitle}
                      </p>
                    </div>
                    <p className="text-sm text-muted-foreground leading-relaxed">
                      {element.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Clean Bottom Quote */}
          <div className="text-center">
            <div className="max-w-3xl mx-auto">
              <div className="relative bg-background/50 rounded-xl p-8 border border-border/30 backdrop-blur-sm">
                <blockquote className="text-xl md:text-2xl font-light text-muted-foreground leading-relaxed">
                  Shaping the leaders of tomorrow through excellence in education,
                  innovation in thinking, and integrity in action.
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 