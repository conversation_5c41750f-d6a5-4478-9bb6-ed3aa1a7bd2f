'use client'

import { Badge } from "@/components/ui/badge"
import { Globe, Lightbulb, Users, Cpu, Heart, BookOpen, Brain, Palette } from "lucide-react"

const visionElements = [
  {
    icon: <Cpu className="h-12 w-12" />,
    title: "Technologically Advanced",
    subtitle: "Innovation at Core",
    description: "Embracing cutting-edge technology to shape tomorrow's solutions",
    color: "text-blue-600",
    accentColor: "border-l-blue-400",
    bgPattern: "bg-gradient-to-br from-blue-50/60 to-cyan-50/40",
    hoverBg: "hover:from-blue-50/80 hover:to-cyan-50/60",
  },
  {
    icon: <Heart className="h-12 w-12" />,
    title: "Ethically Grounded",
    subtitle: "Moral Foundation",
    description: "Building character alongside competence in every endeavor",
    color: "text-crimson",
    accentColor: "border-l-rose-400",
    bgPattern: "bg-gradient-to-br from-red-50/60 to-pink-50/40",
    hoverBg: "hover:from-red-50/80 hover:to-pink-50/60",
  },
  {
    icon: <BookOpen className="h-12 w-12" />,
    title: "Interdisciplinary Learning",
    subtitle: "Beyond Boundaries",
    description: "Weaving knowledge across fields to create holistic understanding",
    color: "text-purple-600",
    accentColor: "border-l-purple-400",
    bgPattern: "bg-gradient-to-br from-purple-50/60 to-indigo-50/40",
    hoverBg: "hover:from-purple-50/80 hover:to-indigo-50/60",
  },
  {
    icon: <Lightbulb className="h-12 w-12" />,
    title: "Creative Problem-Solving",
    subtitle: "Innovative Thinking",
    description: "Transforming challenges into opportunities through creative insight",
    color: "text-amber-600",
    accentColor: "border-l-amber-400",
    bgPattern: "bg-gradient-to-br from-yellow-50/60 to-orange-50/40",
    hoverBg: "hover:from-yellow-50/80 hover:to-orange-50/60",
  },
  {
    icon: <Globe className="h-12 w-12" />,
    title: "Global Mindset",
    subtitle: "World Perspective",
    description: "Cultivating citizens who think locally and act globally",
    color: "text-green-600",
    accentColor: "border-l-emerald-400",
    bgPattern: "bg-gradient-to-br from-green-50/60 to-emerald-50/40",
    hoverBg: "hover:from-green-50/80 hover:to-emerald-50/60",
  },
  {
    icon: <Brain className="h-12 w-12" />,
    title: "Deep Thinking",
    subtitle: "Intellectual Depth",
    description: "Nurturing contemplative minds that question and understand",
    color: "text-indigo-600",
    accentColor: "border-l-indigo-400",
    bgPattern: "bg-gradient-to-br from-indigo-50/60 to-blue-50/40",
    hoverBg: "hover:from-indigo-50/80 hover:to-blue-50/60",
  },
]

export default function VisionSection() {
  return (
    <section id="vision" className="py-24 md:py-32 lg:py-40 bg-gradient-to-br from-background via-muted/5 to-background relative overflow-hidden scroll-mt-20">
      {/* Enhanced Background Elements */}
      <div className="absolute inset-0">
        {/* Elegant geometric pattern */}
        <div className="absolute inset-0 opacity-[0.015]" style={{
          backgroundImage: `
            radial-gradient(circle at 20% 30%, #c82f48 1px, transparent 1px),
            radial-gradient(circle at 80% 70%, #f89c0e 1px, transparent 1px),
            radial-gradient(circle at 60% 20%, #c82f48 0.5px, transparent 0.5px)
          `,
          backgroundSize: '120px 120px, 140px 140px, 80px 80px'
        }}></div>

        {/* Floating orbs with gradient */}
        <div className="absolute top-1/6 right-1/5 w-96 h-96 bg-gradient-to-br from-crimson/8 via-crimson/4 to-transparent rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/6 left-1/5 w-80 h-80 bg-gradient-to-br from-gold/10 via-gold/5 to-transparent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-br from-crimson/6 via-gold/6 to-transparent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '4s' }}></div>

        {/* Subtle grid overlay */}
        <div className="absolute inset-0 opacity-[0.008]" style={{
          backgroundImage: `
            linear-gradient(rgba(200, 47, 72, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(200, 47, 72, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }}></div>
      </div>

      <div className="px-6 md:px-8 lg:px-12 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Elegant Header */}
          <div className="text-center mb-20 md:mb-24">
            {/* Enhanced Badge with glow effect */}
            <div className="inline-flex items-center gap-6 mb-12">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-crimson/40 to-crimson/20"></div>
              <div className="relative">
                <div className="absolute inset-0 bg-crimson/20 rounded-full blur-lg"></div>
                <Badge variant="outline" className="relative px-6 py-3 font-medium border-crimson/30 text-crimson bg-gradient-to-r from-crimson/5 to-gold/5 backdrop-blur-sm shadow-lg">
                  <Palette className="h-4 w-4 mr-2" />
                  Our Vision
                </Badge>
              </div>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-crimson/40 to-crimson/20"></div>
            </div>

            {/* Stunning Typography */}
            <div className="space-y-8">
              <h2 className="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight leading-[0.9]">
                <span className="block text-foreground">Setting Standards of</span>
                <span className="block bg-gradient-to-r from-crimson via-crimson to-gold bg-clip-text text-transparent drop-shadow-sm">
                  Excellence
                </span>
              </h2>

              {/* Beautiful description with enhanced typography */}
              <div className="max-w-4xl mx-auto space-y-6">
                <p className="text-xl md:text-2xl text-muted-foreground leading-relaxed font-light">
                  Ullens College aims to set standards of excellence by nurturing{" "}
                  <span className="relative inline-block">
                    <span className="text-foreground font-medium">technologically advanced</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-400/60 to-purple-400/60"></div>
                  </span>{" "}
                  and{" "}
                  <span className="relative inline-block">
                    <span className="text-crimson font-medium">ethically grounded</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-crimson/60 to-pink-400/60"></div>
                  </span>{" "}
                  graduates
                </p>
                <p className="text-lg md:text-xl text-muted-foreground/90 leading-relaxed">
                  who embrace{" "}
                  <span className="relative inline-block">
                    <span className="text-foreground font-medium">interdisciplinary learning</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-purple-400/60 to-indigo-400/60"></div>
                  </span>
                  , think deeply, solve problems creatively, and act with a{" "}
                  <span className="relative inline-block">
                    <span className="text-gold font-medium">global mindset</span>
                    <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-gold/60 to-amber-400/60"></div>
                  </span>{" "}
                  in a rapidly changing world.
                </p>
              </div>
            </div>
          </div>

          {/* Beautiful Vision Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
            {visionElements.map((element, index) => (
              <div
                key={index}
                className={`group relative overflow-hidden ${element.bgPattern} ${element.hoverBg} rounded-2xl p-8 border-l-4 ${element.accentColor} shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 backdrop-blur-sm border border-white/20`}
                style={{
                  animationDelay: `${index * 0.1}s`
                }}
              >
                {/* Subtle glow effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                {/* Floating icon background */}
                <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-white/10 to-transparent rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                <div className="relative z-10">
                  <div className="flex items-start gap-6 mb-6">
                    <div className={`${element.color} opacity-90 flex-shrink-0 transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 p-3 rounded-xl bg-white/10 backdrop-blur-sm`}>
                      {element.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-foreground leading-tight group-hover:text-opacity-95 transition-colors mb-2">
                        {element.title}
                      </h3>
                      <p className={`text-sm ${element.color} font-semibold opacity-80 tracking-wide`}>
                        {element.subtitle}
                      </p>
                    </div>
                  </div>

                  <p className="text-base text-muted-foreground leading-relaxed group-hover:text-foreground/80 transition-colors duration-300">
                    {element.description}
                  </p>

                  {/* Decorative bottom accent */}
                  <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </div>
              </div>
            ))}
          </div>

          {/* Elegant Closing Quote */}
          <div className="text-center">
            <div className="max-w-4xl mx-auto">
              <div className="relative bg-gradient-to-br from-background/80 via-muted/10 to-background/80 rounded-3xl p-12 md:p-16 border border-border/20 backdrop-blur-sm shadow-2xl">
                {/* Decorative quote marks */}
                <div className="absolute -top-6 -left-6 text-6xl text-crimson/20 font-serif leading-none">"</div>
                <div className="absolute -bottom-6 -right-6 text-6xl text-gold/20 font-serif leading-none transform rotate-180">"</div>

                {/* Elegant background pattern */}
                <div className="absolute inset-0 opacity-[0.03]" style={{
                  backgroundImage: `radial-gradient(circle at 50% 50%, #c82f48 1px, transparent 1px)`,
                  backgroundSize: '40px 40px'
                }}></div>

                <div className="relative z-10">
                  <blockquote className="text-2xl md:text-3xl lg:text-4xl font-light text-muted-foreground leading-relaxed italic mb-6">
                    Shaping the leaders of tomorrow through{" "}
                    <span className="text-crimson font-medium not-italic">excellence in education</span>,{" "}
                    <span className="text-gold font-medium not-italic">innovation in thinking</span>, and{" "}
                    <span className="text-foreground font-medium not-italic">integrity in action</span>.
                  </blockquote>

                  {/* Attribution */}
                  <div className="flex items-center justify-center gap-4 mt-8">
                    <div className="w-12 h-[1px] bg-gradient-to-r from-transparent to-crimson/30"></div>
                    <p className="text-sm font-medium text-muted-foreground uppercase tracking-wider">
                      Ullens College Mission
                    </p>
                    <div className="w-12 h-[1px] bg-gradient-to-l from-transparent to-crimson/30"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 