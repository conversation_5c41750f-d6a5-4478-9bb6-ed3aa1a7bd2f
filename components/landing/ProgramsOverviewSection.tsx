import ProgramOverviewCard from "./ProgramOverviewCard"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Lock, Leaf, Building, BookOpen, Sparkles, Target } from "lucide-react"
import { Badge } from "@/components/ui/badge"

const programsData = [
  {
    title: "Computer Science",
    description: "Technology & Innovation",
    details: "Our flagship B.Tech in Cybersecurity program is now available, with Computer Engineering and Artificial Intelligence programs launching soon.",
    icon: <Lock className="h-6 w-6" />,
    themeColor: "crimson",
    upcoming: false,
    stats: { programs: "1 Available + 2 Coming Soon" },
    gradient: "from-red-500/20 to-pink-500/20",
    accentColor: "border-red-500/30",
    highlights: ["B.Tech Cybersecurity", "Computer Engineering (Soon)", "Artificial Intelligence (Soon)"]
  },
  {
    title: "Agriculture and Climate Science",
    description: "Sustainable Practices",
    details: "Pioneer sustainable agriculture and climate solutions to address global food security and environmental challenges.",
    icon: <Leaf className="h-6 w-6" />,
    themeColor: "green-600",
    upcoming: true,
    stats: { programs: "Coming Soon" },
    gradient: "from-green-500/20 to-emerald-500/20",
    accentColor: "border-green-500/30",
    highlights: ["Sustainable Farming", "Climate Science", "Food Security"]
  },
  {
    title: "Business",
    description: "Leadership & Management",
    details: "Develop strategic thinking, leadership skills, and business acumen to drive innovation in the global marketplace.",
    icon: <Building className="h-6 w-6" />,
    themeColor: "gold",
    upcoming: true,
    stats: { programs: "Coming Soon" },
    gradient: "from-yellow-500/20 to-orange-500/20",
    accentColor: "border-yellow-500/30",
    highlights: ["Strategic Management", "Entrepreneurship", "Global Business"]
  },
  {
    title: "Education",
    description: "Teaching & Learning",
    details: "Our comprehensive Ullens Center for Educator Development prepares passionate educators through innovative teaching methods and educational technology.",
    icon: <BookOpen className="h-6 w-6" />,
    themeColor: "blue-500",
    upcoming: false,
    stats: { programs: "1 Available" },
    gradient: "from-blue-500/20 to-indigo-500/20",
    accentColor: "border-blue-500/30",
    highlights: ["Ullens Center for Educator Development", "Innovative Pedagogy", "Educational Technology"]
  },
];

export default function ProgramsOverviewSection() {
  return (
    <section id="programs" className="w-full py-20 md:py-24 relative overflow-hidden bg-gradient-to-b from-background to-muted/8">
      {/* Minimal Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Subtle gradient backgrounds */}
        <div className="absolute top-1/4 right-1/4 w-80 h-80 bg-gold/4 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-crimson/3 rounded-full blur-3xl"></div>

        {/* Minimal pattern overlay */}
        <div className="absolute inset-0 opacity-[0.008]" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px)`,
          backgroundSize: '100px 100px'
        }}></div>
      </div>

      <div className="px-4 md:px-6 relative z-10">
        {/* Clean Header Section */}
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
            {/* Clean Badge */}
            <div className="flex items-center gap-4">
              <div className="w-12 h-[1px] bg-gold/30"></div>
              <Badge className="px-4 py-2 bg-gold/5 border-gold/30 text-gold">
                <Sparkles className="h-4 w-4 mr-2" />
                Our Schools of Excellence
              </Badge>
              <div className="w-12 h-[1px] bg-gold/30"></div>
            </div>

            {/* Clean Typography */}
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Discover Your Path to{" "}
                <span className="text-crimson">
                  Excellence
                </span>
              </h2>
              <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                Explore our interdisciplinary schools that foster{" "}
                <span className="font-medium text-foreground">deep thinking</span>,{" "}
                <span className="font-medium text-crimson">creative problem-solving</span>, and{" "}
                <span className="font-medium text-gold">global perspectives</span>{" "}
                across diverse fields of study.
              </p>
            </div>
          </div>

          {/* Enhanced Programs Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
            {programsData.map((program, index) => (
              <ProgramOverviewCard
                key={program.title}
                title={program.title}
                description={program.description}
                details={program.details}
                icon={program.icon}
                themeColor={program.themeColor}
                upcoming={program.upcoming}
                stats={program.stats}
                gradient={program.gradient}
                accentColor={program.accentColor}
                highlights={program.highlights}
                index={index}
              />
            ))}
          </div>


        </div>
      </div>
    </section>
  )
}