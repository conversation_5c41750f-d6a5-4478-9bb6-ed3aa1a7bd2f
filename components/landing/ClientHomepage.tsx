'use client'

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, BarChart3, <PERSON><PERSON>uationCap, <PERSON><PERSON><PERSON>, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"

import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import HeroSection from "@/components/landing/HeroSection"
import VisionSection from "@/components/landing/VisionSection"
import ProgramsOverviewSection from "@/components/landing/ProgramsOverviewSection"
import CallToActionSection from "@/components/landing/CallToActionSection"

import { Badge } from "@/components/ui/badge"

interface ClientHomepageProps {
  children: React.ReactNode // This will be the FacultySection
}

export default function ClientHomepage({ children }: ClientHomepageProps) {
  useParallaxEffect()

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          <HeroSection />
          <VisionSection />
          
          {/* Clean Tools Section */}
          <section className="py-20 md:py-24 relative overflow-hidden bg-gradient-to-b from-muted/8 to-background">
            {/* Minimal Background Elements */}
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-1/4 top-1/3 w-64 h-64 bg-crimson/4 rounded-full blur-3xl" />
              <div className="absolute left-1/4 bottom-1/3 w-80 h-80 bg-gold/3 rounded-full blur-3xl" />
            </div>

            <div className="px-6 md:px-8 lg:px-12 relative z-10">
              <div className="mb-16 text-center">
                <div className="inline-flex items-center gap-4 mb-8">
                  <div className="w-12 h-[1px] bg-crimson/30"></div>
                  <Badge className="px-4 py-2 border-crimson/30 text-crimson bg-crimson/5" variant="outline">Tools & Resources</Badge>
                  <div className="w-12 h-[1px] bg-crimson/30"></div>
                </div>
                <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 tracking-tight">Explore Your Academic Journey</h2>
                <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                  Discover the right program for your goals and visualize your path from application to graduation.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
                {/* Clean Program Comparison Tool Card */}
                <div className="bg-background/50 rounded-xl shadow-lg border border-border/30 p-6 hover:shadow-xl hover:border-crimson/30 transition-all duration-300 group backdrop-blur-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="h-12 w-12 rounded-lg bg-crimson/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                      <BarChart3 className="h-6 w-6 text-crimson" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-1">Program Comparison Tool</h3>
                      <Badge variant="outline" className="bg-crimson/5 text-crimson border-crimson/30 text-xs">
                        <Sparkles className="h-3 w-3 mr-1" /> Interactive
                      </Badge>
                    </div>
                  </div>

                  <p className="mb-6 text-muted-foreground leading-relaxed">
                    Compare different programs side-by-side to find the perfect fit for your academic goals and interests.
                  </p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    <Badge variant="secondary" className="text-xs">Compare Curriculums</Badge>
                    <Badge variant="secondary" className="text-xs">Requirements</Badge>
                    <Badge variant="secondary" className="text-xs">Career Paths</Badge>
                  </div>

                  <Link href="/programs/compare" className="block">
                    <Button className="w-full bg-crimson hover:bg-crimson/90 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                      Compare Programs <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
                
                {/* Clean Student Journey Card */}
                <div className="bg-background/50 rounded-xl shadow-lg border border-border/30 p-6 hover:shadow-xl hover:border-gold/30 transition-all duration-300 group backdrop-blur-sm">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="h-12 w-12 rounded-lg bg-gold/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                      <GraduationCap className="h-6 w-6 text-gold" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-1">Student Journey</h3>
                      <Badge variant="outline" className="bg-gold/5 text-gold border-gold/30 text-xs">
                        <Clock className="h-3 w-3 mr-1" /> Timeline
                      </Badge>
                    </div>
                  </div>

                  <p className="mb-6 text-muted-foreground leading-relaxed">
                    Explore the student experience from application to graduation with our interactive timeline and resources.
                  </p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    <Badge variant="secondary" className="text-xs">Application</Badge>
                    <Badge variant="secondary" className="text-xs">Coursework</Badge>
                    <Badge variant="secondary" className="text-xs">Graduation</Badge>
                  </div>

                  <Link href="/student-journey" className="block">
                    <Button className="w-full bg-gold hover:bg-gold/90 text-white shadow-lg hover:shadow-xl transition-all duration-300">
                      View Student Journey <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>
          
          <ProgramsOverviewSection />
          
          {children} {/* This will be the server-side FacultySection and PostsSection */}
          <CallToActionSection id="apply" />
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}
