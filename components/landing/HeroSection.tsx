'use client'

export default function HeroSection() {
  return (
    <section className="min-h-screen relative overflow-hidden bg-gradient-to-b from-background to-muted/5">
      {/* Simplified Background Elements */}
      <div className="absolute inset-0">
        {/* Minimal pattern overlay */}
        <div className="absolute inset-0 opacity-[0.008]" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px)`,
          backgroundSize: '120px 120px'
        }}></div>

        {/* Subtle ambient elements */}
        <div className="absolute top-1/3 right-1/4 w-96 h-96 bg-crimson/3 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/3 w-80 h-80 bg-gold/4 rounded-full blur-3xl"></div>
      </div>

      <div className="relative z-10 px-6 md:px-8 lg:px-12 h-full min-h-screen flex items-center">
        <div className="max-w-7xl mx-auto w-full">
          {/* Simplified Clean Layout */}
          <div className="grid grid-cols-12 gap-8 lg:gap-16 items-center min-h-[80vh]">

            {/* Left Column - Clean Typography */}
            <div className="col-span-12 lg:col-span-7 space-y-12 lg:space-y-16">

              {/* Simplified Masthead */}
              <div className="flex items-center gap-4">
                <div className="w-12 h-[1px] bg-crimson/40"></div>
                <div className="text-sm font-medium tracking-wide text-muted-foreground uppercase px-3 py-1 rounded-full bg-muted/10">
                  Est. 2025
                </div>
                <div className="w-12 h-[1px] bg-crimson/40"></div>
              </div>

              {/* Clean Typography */}
              <div className="space-y-8">
                <div className="space-y-4">
                  <div className="text-lg font-medium text-muted-foreground uppercase tracking-wide">
                    Welcome to
                  </div>
                  <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold leading-[0.85] tracking-tight">
                    <span className="text-foreground">ULLENS</span>
                  </h1>
                  <div className="text-4xl md:text-5xl lg:text-6xl font-light text-crimson tracking-wide">
                    College
                  </div>
                </div>
              </div>

              {/* Clean Tagline */}
              <div className="relative py-8">
                <div className="relative">
                  <blockquote className="text-xl md:text-2xl lg:text-3xl font-light text-muted-foreground leading-relaxed">
                    <span className="text-foreground font-medium">Think Deeply.</span>{" "}
                    <span className="text-crimson font-medium">Create Boldly.</span>{" "}
                    <span className="text-gold font-medium">Lead Globally.</span>
                  </blockquote>
                  <div className="mt-4 text-base text-muted-foreground/80 font-light">
                    Where innovation meets integrity
                  </div>
                </div>
              </div>

              {/* Simplified Stats */}
              <div className="relative">
                <div className="grid grid-cols-3 gap-6 py-8 px-6 border border-border/30 rounded-2xl bg-background/50 backdrop-blur-sm">
                  <div className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-crimson mb-2">4</div>
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium">Schools of Excellence</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-gold mb-2">∞</div>
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium">Boundless Futures</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl md:text-4xl font-bold text-foreground mb-2">1</div>
                    <div className="text-xs text-muted-foreground uppercase tracking-wide font-medium">Unified Vision</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Clean Visual */}
            <div className="col-span-12 lg:col-span-5">
              <div className="relative h-full flex items-center pt-8 lg:pt-0">
                {/* Simplified Campus Showcase */}
                <div className="relative w-full group">
                  {/* Subtle background glow */}
                  <div className="absolute -inset-8 bg-gradient-to-br from-crimson/5 to-gold/5 rounded-3xl blur-2xl opacity-50"></div>

                  {/* Clean Campus Display */}
                  <div className="relative overflow-hidden rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-300 border border-border/20">
                    {/* Campus Image */}
                    <div className="relative h-80 md:h-96 lg:h-[26rem] overflow-hidden">
                      <img
                        src="/images/campus/campus-aerial-view.png"
                        alt="Ullens College Campus - Architectural Excellence and Innovation"
                        className="w-full h-full object-cover object-center hover:scale-105 transition-transform duration-500"
                      />

                      {/* Minimal overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}