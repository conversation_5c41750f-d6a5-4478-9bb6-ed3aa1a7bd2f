'use client'

export default function HeroSection() {
  return (
    <section className="min-h-screen relative overflow-hidden bg-gradient-to-br from-background via-muted/3 to-background">
      {/* Enhanced Aesthetic Background Elements */}
      <div className="absolute inset-0">
        {/* Sophisticated geometric pattern */}
        <div className="absolute inset-0 opacity-[0.02]" style={{
          backgroundImage: `
            radial-gradient(circle at 20% 20%, #c82f48 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, #f89c0e 1px, transparent 1px),
            radial-gradient(circle at 40% 60%, #c82f48 0.5px, transparent 0.5px)
          `,
          backgroundSize: '150px 150px, 200px 200px, 100px 100px'
        }}></div>

        {/* Beautiful floating orbs with gradients */}
        <div className="absolute top-1/5 right-1/6 w-[500px] h-[500px] bg-gradient-to-br from-crimson/8 via-crimson/4 to-transparent rounded-full blur-3xl animate-gentle-float"></div>
        <div className="absolute bottom-1/5 left-1/6 w-[400px] h-[400px] bg-gradient-to-br from-gold/10 via-gold/5 to-transparent rounded-full blur-3xl animate-gentle-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[300px] h-[300px] bg-gradient-to-br from-crimson/6 via-gold/6 to-transparent rounded-full blur-3xl animate-gentle-float" style={{ animationDelay: '4s' }}></div>

        {/* Elegant grid overlay */}
        <div className="absolute inset-0 opacity-[0.01]" style={{
          backgroundImage: `
            linear-gradient(rgba(200, 47, 72, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(200, 47, 72, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '80px 80px'
        }}></div>
      </div>

      <div className="relative z-10 px-6 md:px-8 lg:px-12 h-full min-h-screen flex items-center">
        <div className="max-w-8xl mx-auto w-full">
          {/* Beautiful Hero Layout with Perfect Spacing */}
          <div className="grid grid-cols-12 gap-12 lg:gap-20 items-center min-h-[85vh] py-12 lg:py-20">

            {/* Left Column - Stunning Typography */}
            <div className="col-span-12 lg:col-span-7 space-y-16 lg:space-y-20">

              {/* Elegant Masthead with Enhanced Spacing */}
              <div className="flex items-center gap-6 animate-fade-in-up">
                <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-crimson/50 to-crimson/30"></div>
                <div className="relative">
                  <div className="absolute inset-0 bg-crimson/10 rounded-full blur-md"></div>
                  <div className="relative text-sm font-semibold tracking-[0.2em] text-muted-foreground uppercase px-4 py-2 rounded-full bg-gradient-to-r from-crimson/5 to-gold/5 border border-crimson/20 backdrop-blur-sm">
                    Est. 2025 — Pioneering Excellence
                  </div>
                </div>
                <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-crimson/50 to-crimson/30"></div>
              </div>

              {/* Magnificent Typography with Perfect Hierarchy */}
              <div className="space-y-12 animate-fade-in-up" style={{ animationDelay: '0.2s' }}>
                <div className="space-y-8">
                  {/* Welcome Text with Enhanced Spacing */}
                  <div className="text-xl md:text-2xl font-light text-muted-foreground uppercase tracking-[0.3em] opacity-90 mb-8">
                    Welcome to
                  </div>

                  {/* Main College Name with Dramatic Typography */}
                  <div className="space-y-4">
                    <h1 className="text-7xl md:text-8xl lg:text-9xl font-black leading-[0.8] tracking-tighter">
                      <span className="block text-foreground drop-shadow-sm">ULLENS</span>
                    </h1>
                    <div className="text-5xl md:text-6xl lg:text-7xl font-light text-crimson tracking-wide ml-2 md:ml-4 drop-shadow-sm">
                      College
                    </div>
                  </div>
                </div>
              </div>

              {/* Beautiful Tagline with Enhanced Design */}
              <div className="relative py-12 animate-fade-in-up" style={{ animationDelay: '0.4s' }}>
                {/* Elegant quote marks */}
                <div className="absolute -left-8 -top-4 text-8xl text-crimson/15 font-serif leading-none">"</div>
                <div className="absolute -right-8 -bottom-4 text-8xl text-gold/15 font-serif leading-none transform rotate-180">"</div>

                <div className="relative pl-12 pr-12">
                  <blockquote className="text-2xl md:text-3xl lg:text-4xl font-light text-muted-foreground leading-relaxed mb-8">
                    <span className="relative inline-block">
                      <span className="text-foreground font-semibold">Think Deeply.</span>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-400/40 to-purple-400/40 rounded-full"></div>
                    </span>{" "}
                    <span className="relative inline-block">
                      <span className="text-crimson font-semibold">Create Boldly.</span>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-crimson/40 to-pink-400/40 rounded-full"></div>
                    </span>{" "}
                    <span className="relative inline-block">
                      <span className="text-gold font-semibold">Lead Globally.</span>
                      <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-gold/40 to-amber-400/40 rounded-full"></div>
                    </span>
                  </blockquote>

                  {/* Enhanced subtitle with better spacing */}
                  <div className="flex items-center gap-4 mt-8">
                    <div className="w-12 h-[1px] bg-gradient-to-r from-transparent to-muted-foreground/30"></div>
                    <p className="text-lg md:text-xl text-muted-foreground/90 font-light italic">
                      Where innovation meets integrity
                    </p>
                    <div className="w-12 h-[1px] bg-gradient-to-l from-transparent to-muted-foreground/30"></div>
                  </div>
                </div>
              </div>

              {/* Stunning Stats Section with Enhanced Design */}
              <div className="relative animate-fade-in-up" style={{ animationDelay: '0.6s' }}>
                {/* Background glow effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-crimson/5 via-gold/5 to-crimson/5 rounded-3xl blur-xl"></div>

                <div className="relative grid grid-cols-3 gap-8 py-12 px-8 border border-border/20 rounded-3xl bg-gradient-to-r from-background/80 via-muted/5 to-background/80 backdrop-blur-sm shadow-2xl">
                  {/* Enhanced stat items with better spacing */}
                  <div className="text-center group cursor-default">
                    <div className="text-4xl md:text-5xl font-black text-crimson mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">4</div>
                    <div className="text-xs md:text-sm text-muted-foreground uppercase tracking-[0.15em] font-semibold leading-tight">
                      Schools of<br />Excellence
                    </div>
                  </div>
                  <div className="text-center group cursor-default">
                    <div className="text-4xl md:text-5xl font-black text-gold mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">∞</div>
                    <div className="text-xs md:text-sm text-muted-foreground uppercase tracking-[0.15em] font-semibold leading-tight">
                      Boundless<br />Futures
                    </div>
                  </div>
                  <div className="text-center group cursor-default">
                    <div className="text-4xl md:text-5xl font-black text-foreground mb-4 group-hover:scale-110 transition-all duration-300 drop-shadow-sm">1</div>
                    <div className="text-xs md:text-sm text-muted-foreground uppercase tracking-[0.15em] font-semibold leading-tight">
                      Unified<br />Vision
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Stunning Visual Showcase */}
            <div className="col-span-12 lg:col-span-5">
              <div className="relative h-full flex items-center pt-12 lg:pt-0 animate-fade-in-up" style={{ animationDelay: '0.8s' }}>
                {/* Beautiful Campus Showcase with Enhanced Design */}
                <div className="relative w-full group">
                  {/* Multi-layered background effects */}
                  <div className="absolute -inset-12 bg-gradient-to-br from-crimson/8 via-gold/8 to-crimson/8 rounded-[3rem] blur-3xl opacity-60 group-hover:opacity-80 transition-all duration-700"></div>
                  <div className="absolute -inset-8 bg-gradient-to-br from-gold/6 to-crimson/6 rounded-[2.5rem] blur-2xl opacity-40 group-hover:opacity-60 transition-all duration-700"></div>

                  {/* Elegant Campus Display with Perfect Spacing */}
                  <div className="relative overflow-hidden rounded-3xl shadow-2xl hover:shadow-3xl transition-all duration-500 border border-white/20 backdrop-blur-sm">
                    {/* Enhanced Campus Image Container */}
                    <div className="relative h-96 md:h-[28rem] lg:h-[32rem] overflow-hidden">
                      <img
                        src="/images/campus/campus-aerial-view.png"
                        alt="Ullens College Campus - Architectural Excellence and Innovation"
                        className="w-full h-full object-cover object-center scale-105 group-hover:scale-110 transition-transform duration-700"
                      />

                      {/* Beautiful gradient overlays */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
                      <div className="absolute inset-0 bg-gradient-to-br from-crimson/5 via-transparent to-gold/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                      {/* Elegant shine effect */}
                      <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-shimmer"></div>
                    </div>

                    {/* Decorative bottom accent */}
                    <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-crimson/60 via-gold/60 to-crimson/60"></div>
                  </div>

                  {/* Floating decorative elements with perfect spacing */}
                  <div className="absolute -top-8 -right-8 w-32 h-32 bg-gradient-to-br from-gold/20 to-amber-400/20 rounded-full blur-2xl animate-gentle-float"></div>
                  <div className="absolute -bottom-12 -left-12 w-40 h-40 bg-gradient-to-br from-crimson/15 to-pink-400/15 rounded-full blur-2xl animate-gentle-float" style={{ animationDelay: '2s' }}></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}