import FacultyCard from "./FacultyCard";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowRight, Users, BookOpen, Award, GraduationCap, Sparkles, Star, ChevronRight } from "lucide-react";
import Link from "next/link";
import { prisma } from "@/lib/prisma";

async function getFeaturedFaculty() {
  try {
    // Use the same approach as the main faculty page - query facultyProfile instead of user
    const facultyProfiles = await prisma.facultyProfile.findMany({
      where: {
        user: {
          status: 'ACTIVE'
        }
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
            profile: {
              select: {
                avatarUrl: true
              }
            }
          }
        },
        department: {
          select: {
            name: true,
            slug: true
          }
        },
        researchAreas: {
          select: {
            areaName: true
          }
        },
        publications: {
          select: {
            id: true
          }
        }
      },
      take: 4, // Show only 4 faculty on homepage
      orderBy: {
        user: {
          createdAt: 'asc' // Show the first faculty members added
        }
      }
    });

    return facultyProfiles.map(faculty => ({
      id: faculty.user.id,
      imageUrl: faculty.user.profile?.avatarUrl || faculty.user.image || "/images/faculty/default-avatar.svg",
      altText: faculty.user.name || "Faculty Member",
      name: faculty.user.name || "Faculty Member",
      title: faculty.title || "Faculty Member",
      bio: faculty.bio || "Dedicated educator and researcher.",
      department: faculty.department.name,
      departmentSlug: faculty.department.slug,
      researchAreas: faculty.researchAreas.map(area => area.areaName).slice(0, 3),
      publicationsCount: faculty.publications.length
    }));
  } catch (error) {
    console.error('Error fetching featured faculty:', error);
    // Fallback to empty array if database fails
    return [];
  }
}

export default async function FacultySection() {
  const facultyData = await getFeaturedFaculty();

  // If no faculty data, show a placeholder message
  if (facultyData.length === 0) {
    return (
      <section id="faculty" className="w-full py-24 md:py-32 lg:py-40 relative overflow-hidden bg-gradient-to-b from-background via-muted/5 to-background">
        <div className="px-4 md:px-6 relative">
          <div className="flex flex-col items-center justify-center space-y-6 text-center">
            <div className="flex items-center gap-4">
              <div className="w-16 h-[1px] bg-gradient-to-r from-transparent via-[#c82f48] to-transparent"></div>
              <div className="px-6 py-3 bg-gradient-to-r from-[#c82f48]/20 to-pink-500/20 border border-[#c82f48]/50 rounded-full backdrop-blur-sm font-semibold text-[#c82f48] flex items-center hover:from-[#c82f48]/30 hover:to-pink-500/30 transition-all duration-300">
                <Users className="h-4 w-4 mr-2 text-[#c82f48]" />
                <span className="text-[#c82f48] font-semibold">Our Distinguished Faculty</span>
              </div>
              <div className="w-16 h-[1px] bg-gradient-to-l from-transparent via-[#c82f48] to-transparent"></div>
            </div>
            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Meet Our <span className="bg-gradient-to-r from-crimson via-pink-500 to-purple-500 bg-clip-text text-transparent">Faculty</span>
              </h2>
              <p className="text-xl text-muted-foreground leading-relaxed max-w-2xl mx-auto">
                Our faculty profiles are being updated. Please check back soon to meet our distinguished educators.
              </p>
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section id="faculty" className="w-full py-20 md:py-24 relative overflow-hidden bg-gradient-to-b from-background to-muted/8">
      {/* Minimal Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Subtle gradient backgrounds */}
        <div className="absolute top-1/4 right-1/4 w-80 h-80 bg-crimson/4 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 left-1/4 w-64 h-64 bg-gold/3 rounded-full blur-3xl"></div>

        {/* Minimal pattern overlay */}
        <div className="absolute inset-0 opacity-[0.006]" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, #c82f48 1px, transparent 1px)`,
          backgroundSize: '80px 80px'
        }}></div>
      </div>

      <div className="px-4 md:px-6 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Clean Header Section */}
          <div className="flex flex-col items-center justify-center space-y-8 text-center mb-16">
            <div className="flex items-center gap-4">
              <div className="w-12 h-[1px] bg-crimson/30"></div>
              <Badge className="px-4 py-2 bg-crimson/5 border-crimson/30 text-crimson">
                <Users className="h-4 w-4 mr-2" />
                Our Distinguished Faculty
              </Badge>
              <div className="w-12 h-[1px] bg-crimson/30"></div>
            </div>

            <div className="space-y-6">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight">
                Meet Our <span className="text-crimson">Faculty</span>
              </h2>
              <p className="text-lg md:text-xl text-muted-foreground leading-relaxed max-w-3xl mx-auto">
                Our distinguished faculty members are{" "}
                <span className="font-medium text-foreground">leaders in their fields</span>,{" "}
                dedicated to{" "}
                <span className="font-medium text-crimson">excellence in teaching</span>{" "}
                and{" "}
                <span className="font-medium text-gold">groundbreaking research</span>.
              </p>
            </div>
          </div>

          {/* Clean Faculty Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {facultyData.map((faculty, index) => (
              <div key={faculty.id} className="group">
                <Link href={`/faculty/${faculty.id}`} className="block">
                  <div className="relative overflow-hidden rounded-xl bg-background/50 backdrop-blur-sm border border-border/30 shadow-lg transition-all duration-300 hover:shadow-xl hover:border-crimson/30 hover:-translate-y-1">
                    {/* Faculty Image */}
                    <div className="aspect-[3/4] overflow-hidden relative">
                      <img
                        src={faculty.imageUrl}
                        alt={faculty.altText}
                        className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                      />
                      {/* Minimal overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    {/* Faculty Info */}
                    <div className="p-5 space-y-3">
                      <div>
                        <h3 className="text-lg font-semibold text-foreground group-hover:text-crimson transition-colors duration-300 leading-tight">
                          {faculty.name}
                        </h3>
                        <p className="text-muted-foreground font-medium mt-1 text-sm">{faculty.title}</p>
                        <p className="text-xs text-muted-foreground/80 mt-1">{faculty.department}</p>
                      </div>

                      {/* Research Areas */}
                      {faculty.researchAreas && faculty.researchAreas.length > 0 && (
                        <div className="space-y-2">
                          <div className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Research Areas</div>
                          <div className="flex flex-wrap gap-1">
                            {faculty.researchAreas.slice(0, 2).map((area, idx) => (
                              <Badge
                                key={idx}
                                variant="outline"
                                className="text-xs px-2 py-1 border-border/50 text-muted-foreground"
                              >
                                {area}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Publications Count */}
                      {faculty.publicationsCount > 0 && (
                        <div className="flex items-center gap-2 text-xs text-muted-foreground">
                          <BookOpen className="h-3 w-3" />
                          <span>{faculty.publicationsCount} Publications</span>
                        </div>
                      )}

                      {/* Bio Preview */}
                      <p className="text-xs text-muted-foreground leading-relaxed line-clamp-2">
                        {faculty.bio}
                      </p>

                      {/* Learn More Link */}
                      <div className="flex items-center gap-2 text-crimson font-medium text-xs pt-2">
                        <span>Learn More</span>
                        <ChevronRight className="h-3 w-3 group-hover:translate-x-1 transition-transform duration-300" />
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>

          {/* Clean CTA Section */}
          <div className="text-center">
            <div className="inline-flex flex-col items-center gap-6 p-6 bg-background/50 backdrop-blur-sm rounded-xl border border-border/30 shadow-lg">
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-lg font-semibold text-foreground">
                  <Sparkles className="h-4 w-4 text-crimson" />
                  <span>Discover Our Complete Faculty</span>
                </div>
                <p className="text-muted-foreground max-w-md text-sm">
                  Explore detailed profiles, research interests, and academic achievements of all our distinguished faculty members.
                </p>
              </div>
              <Link href="/faculty">
                <Button
                  size="lg"
                  className="bg-crimson hover:bg-crimson/90 shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  <Users className="mr-2 h-4 w-4" />
                  Meet All Faculty
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>

    </section>
  );
}