import { PrismaClient, PostStatus } from '@prisma/client'

const prisma = new PrismaClient()

async function createCodeFormattingTestPost() {
  console.log('Creating code formatting test post...')

  try {
    // Find <PERSON><PERSON>kari's user account
    const monilUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!monilUser) {
      console.error('Mon<PERSON> Adhikari user not found!')
      return
    }

    // Find a suitable category
    const category = await prisma.category.findFirst({
      where: { slug: 'college-news' }
    })

    if (!category) {
      console.error('No suitable category found!')
      return
    }

    // Check if test post already exists
    const existingPost = await prisma.post.findFirst({
      where: {
        authorId: monilUser.id,
        slug: 'code-formatting-test'
      }
    })

    if (existingPost) {
      console.log('Code formatting test post already exists!')
      console.log(`Post ID: ${existingPost.id}`)
      return existingPost
    }

    // Create the test post with various code examples
    const testPost = await prisma.post.create({
      data: {
        title: 'Code Formatting Test',
        slug: 'code-formatting-test',
        content: `# Code Formatting Test

This post tests the improved code formatting with various programming languages.

## Python Example

Here's a Python function with proper syntax highlighting:

\`\`\`python
def fibonacci(n):
    """Generate Fibonacci sequence up to n terms."""
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    sequence = [0, 1]
    for i in range(2, n):
        next_num = sequence[i-1] + sequence[i-2]
        sequence.append(next_num)
    
    return sequence

# Example usage
result = fibonacci(10)
print(f"First 10 Fibonacci numbers: {result}")
\`\`\`

## JavaScript Example

Here's a modern JavaScript example with ES6+ features:

\`\`\`javascript
class DataProcessor {
    constructor(data) {
        this.data = data;
        this.processed = false;
    }

    async processData() {
        try {
            const results = await Promise.all(
                this.data.map(async (item) => {
                    const response = await fetch(\`/api/process/\${item.id}\`);
                    return response.json();
                })
            );
            
            this.processed = true;
            return results.filter(result => result.success);
        } catch (error) {
            console.error('Processing failed:', error);
            throw new Error('Data processing failed');
        }
    }

    get isProcessed() {
        return this.processed;
    }
}

// Usage example
const processor = new DataProcessor(sampleData);
processor.processData().then(results => {
    console.log('Processed results:', results);
});
\`\`\`

## SQL Example

Database query with proper formatting:

\`\`\`sql
SELECT 
    u.id,
    u.name,
    u.email,
    p.title as post_title,
    p.created_at,
    COUNT(c.id) as comment_count
FROM users u
LEFT JOIN posts p ON u.id = p.author_id
LEFT JOIN comments c ON p.id = c.post_id
WHERE u.status = 'ACTIVE'
    AND p.published_at IS NOT NULL
    AND p.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY u.id, p.id
HAVING comment_count > 0
ORDER BY p.created_at DESC, comment_count DESC
LIMIT 50;
\`\`\`

## TypeScript Example

Type-safe React component:

\`\`\`typescript
interface User {
    id: string;
    name: string;
    email: string;
    role: 'ADMIN' | 'FACULTY' | 'STUDENT';
}

interface UserCardProps {
    user: User;
    onEdit?: (user: User) => void;
    onDelete?: (userId: string) => void;
}

const UserCard: React.FC<UserCardProps> = ({ user, onEdit, onDelete }) => {
    const handleEdit = useCallback(() => {
        onEdit?.(user);
    }, [user, onEdit]);

    const handleDelete = useCallback(() => {
        if (window.confirm(\`Delete user \${user.name}?\`)) {
            onDelete?.(user.id);
        }
    }, [user, onDelete]);

    return (
        <div className="user-card">
            <h3>{user.name}</h3>
            <p>{user.email}</p>
            <span className={\`role role--\${user.role.toLowerCase()}\`}>
                {user.role}
            </span>
            <div className="actions">
                <button onClick={handleEdit}>Edit</button>
                <button onClick={handleDelete}>Delete</button>
            </div>
        </div>
    );
};
\`\`\`

## Inline Code

You can also use inline code like \`const variable = "value"\` or \`npm install package-name\` within sentences.

## Bash/Shell Example

Command line operations:

\`\`\`bash
#!/bin/bash

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env

# Run database migrations
npx prisma migrate dev

# Start the development server
npm run dev

# In another terminal, run tests
npm test -- --watch
\`\`\`

This demonstrates the improved code formatting with:
- ✅ Light theme instead of dark background
- ✅ Copy button functionality
- ✅ Language labels
- ✅ Proper syntax highlighting
- ✅ Clean, readable formatting`,
        excerpt: 'Testing the improved code formatting system with various programming languages and syntax highlighting.',
        authorId: monilUser.id,
        categoryId: category.id,
        status: PostStatus.PUBLISHED,
        featured: false,
        publishedAt: new Date(),
        readingTime: '3 min read'
      }
    })

    console.log('✅ Code formatting test post created successfully!')
    console.log(`Post ID: ${testPost.id}`)
    console.log(`Post Title: ${testPost.title}`)
    console.log(`Post Slug: ${testPost.slug}`)
    console.log(`URL: http://localhost:3001/posts/${testPost.slug}`)
    
    return testPost

  } catch (error) {
    console.error('Error creating test post:', error)
    throw error
  }
}

createCodeFormattingTestPost()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
